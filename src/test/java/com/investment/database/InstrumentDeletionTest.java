package com.investment.database;

import com.investment.service.InstrumentService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * Test class for instrument deletion functionality with foreign key constraints.
 * Tests the improved deletion approach using separate transactions.
 */
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class InstrumentDeletionTest {

    private static final Logger logger = LoggerFactory.getLogger(InstrumentDeletionTest.class);
    
    private DatabaseManager databaseManager;
    private InstrumentService instrumentService;
    private final String testSymbol = "TEST.HK";

    @BeforeEach
    void setUp() throws SQLException {
        // Initialize database manager with in-memory database for testing
        databaseManager = new DatabaseManager(":memory:");
        instrumentService = new InstrumentService(databaseManager);
        
        // Create test instrument
        createTestInstrument();
    }

    private void createTestInstrument() throws SQLException {
        databaseManager.createInstrument(
            testSymbol,
            "Test Company",
            "STOCK",
            new BigDecimal("1000000000"),
            "Hong Kong",
            2020,
            "Technology",
            "Software"
        );
    }

    @Test
    void testDeleteInstrumentWithNoChildRecords() throws SQLException {
        // Test deletion when no child records exist
        String result = instrumentService.deleteInstrument(testSymbol);
        
        assertNotNull(result);
        assertTrue(result.contains("Deleted instrument"));
        assertFalse(databaseManager.symbolExists(testSymbol));
        
        logger.info("Successfully tested deletion with no child records: {}", result);
    }

    @Test
    void testDeleteInstrumentWithOhlcvData() throws SQLException {
        // Add OHLCV data
        databaseManager.saveOHLCVData(java.util.List.of(
            new com.investment.model.OHLCV(testSymbol, LocalDate.now(), 
                new BigDecimal("100"), new BigDecimal("105"), 
                new BigDecimal("95"), new BigDecimal("102"), 1000L)
        ));
        
        // Verify OHLCV data exists
        assertEquals(1, databaseManager.countOhlcvRecords(testSymbol));
        
        // Delete instrument
        String result = instrumentService.deleteInstrument(testSymbol);
        
        assertNotNull(result);
        assertTrue(result.contains("1 OHLCV records"));
        assertFalse(databaseManager.symbolExists(testSymbol));
        assertEquals(0, databaseManager.countOhlcvRecords(testSymbol));
        
        logger.info("Successfully tested deletion with OHLCV data: {}", result);
    }

    @Test
    void testDeleteInstrumentWithPositionData() throws SQLException {
        // Add position data
        Long positionId = databaseManager.createPosition(
            testSymbol,
            new BigDecimal("100"),
            "BUY",
            "OPEN",
            new BigDecimal("100"),
            new BigDecimal("10000"),
            new BigDecimal("50000"),
            new BigDecimal("500"),
            new BigDecimal("0.02"),
            new BigDecimal("0.01")
        );
        
        assertNotNull(positionId);
        assertEquals(1, databaseManager.countPositionsRecords(testSymbol));
        
        // Delete instrument
        String result = instrumentService.deleteInstrument(testSymbol);
        
        assertNotNull(result);
        assertTrue(result.contains("1 positions"));
        assertFalse(databaseManager.symbolExists(testSymbol));
        assertEquals(0, databaseManager.countPositionsRecords(testSymbol));
        
        logger.info("Successfully tested deletion with position data: {}", result);
    }

    @Test
    void testDeleteInstrumentWithWatchlistData() throws SQLException {
        // Add watchlist data
        Long watchlistId = databaseManager.createWatchListItem(
            1,
            testSymbol,
            java.sql.Date.valueOf(LocalDate.now()),
            "Test watchlist item"
        );
        
        assertNotNull(watchlistId);
        assertEquals(1, databaseManager.countWatchlistRecords(testSymbol));
        
        // Delete instrument
        String result = instrumentService.deleteInstrument(testSymbol);
        
        assertNotNull(result);
        assertTrue(result.contains("1 watchlist items"));
        assertFalse(databaseManager.symbolExists(testSymbol));
        assertEquals(0, databaseManager.countWatchlistRecords(testSymbol));
        
        logger.info("Successfully tested deletion with watchlist data: {}", result);
    }

    @Test
    void testDeleteInstrumentWithAllChildData() throws SQLException {
        // Add all types of child data
        
        // OHLCV data
        databaseManager.saveOHLCVData(java.util.List.of(
            new com.investment.model.OHLCV(testSymbol, LocalDate.now(), 
                new BigDecimal("100"), new BigDecimal("105"), 
                new BigDecimal("95"), new BigDecimal("102"), 1000L)
        ));
        
        // Position data
        databaseManager.createPosition(
            testSymbol,
            new BigDecimal("100"),
            "BUY",
            "OPEN",
            new BigDecimal("100"),
            new BigDecimal("10000"),
            new BigDecimal("50000"),
            new BigDecimal("500"),
            new BigDecimal("0.02"),
            new BigDecimal("0.01")
        );
        
        // Watchlist data
        databaseManager.createWatchListItem(
            1,
            testSymbol,
            java.sql.Date.valueOf(LocalDate.now()),
            "Test watchlist item"
        );
        
        // Verify all data exists
        assertEquals(1, databaseManager.countOhlcvRecords(testSymbol));
        assertEquals(1, databaseManager.countPositionsRecords(testSymbol));
        assertEquals(1, databaseManager.countWatchlistRecords(testSymbol));
        
        // Delete instrument
        String result = instrumentService.deleteInstrument(testSymbol);
        
        assertNotNull(result);
        assertTrue(result.contains("1 OHLCV records"));
        assertTrue(result.contains("1 positions"));
        assertTrue(result.contains("1 watchlist items"));
        assertFalse(databaseManager.symbolExists(testSymbol));
        
        // Verify all child data is deleted
        assertEquals(0, databaseManager.countOhlcvRecords(testSymbol));
        assertEquals(0, databaseManager.countPositionsRecords(testSymbol));
        assertEquals(0, databaseManager.countWatchlistRecords(testSymbol));
        
        logger.info("Successfully tested deletion with all child data: {}", result);
    }

    @Test
    void testDeleteNonExistentInstrument() {
        assertThrows(IllegalArgumentException.class, () -> {
            instrumentService.deleteInstrument("NONEXISTENT");
        });
    }

    @Test
    void testDeleteInstrumentWithNullSymbol() {
        assertThrows(IllegalArgumentException.class, () -> {
            instrumentService.deleteInstrument(null);
        });
    }

    @Test
    void testDeleteInstrumentWithEmptySymbol() {
        assertThrows(IllegalArgumentException.class, () -> {
            instrumentService.deleteInstrument("");
        });
    }
}
